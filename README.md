# 企业微信打卡助手

一个帮助实现企业微信自动打卡的Android应用，支持位置模拟和定时打卡功能。

## 功能特性

- 🌍 **位置模拟**: 修改GPS定位到指定的打卡地点
- ⏰ **定时打卡**: 自动在设定时间提醒或执行打卡
- 🤖 **智能识别**: 使用无障碍服务自动识别打卡按钮
- 📱 **简洁界面**: 直观易用的操作界面
- 🔒 **安全可靠**: 本地运行，不上传任何数据

## 安装要求

- Android 7.0 (API 24) 或更高版本
- 已安装企业微信应用
- 开启开发者选项和模拟位置功能

## 使用步骤

### 1. 准备工作

1. **开启开发者选项**:
   - 进入设置 → 关于手机
   - 连续点击"版本号"7次开启开发者选项

2. **启用模拟位置**:
   - 进入设置 → 开发者选项
   - 找到"选择模拟位置信息应用"
   - 选择"企业微信打卡助手"

3. **开启无障碍服务**:
   - 进入设置 → 无障碍
   - 找到"企业微信打卡助手"
   - 开启无障碍服务

### 2. 配置位置

1. 打开应用
2. 在"位置设置"区域输入公司的经纬度坐标
3. 填写地址描述（可选）
4. 点击"设置位置"按钮

### 3. 启动自动打卡

1. 点击"启动自动打卡"按钮
2. 应用会在上班时间(9:00)和下班时间(18:00)自动提醒打卡
3. 需要时可以点击"停止自动打卡"

## 获取经纬度坐标

可以通过以下方式获取公司的经纬度坐标：

1. **高德地图**: 在地图上长按位置，复制坐标
2. **百度地图**: 使用坐标拾取工具
3. **Google地图**: 右键点击位置查看坐标
4. **在线工具**: 搜索"经纬度查询"使用在线工具

## 注意事项

⚠️ **重要提醒**:
- 本应用仅供学习和研究使用
- 请遵守公司考勤制度和相关法律法规
- 不建议在正式工作环境中使用
- 使用前请了解相关风险和后果

## 技术实现

- **位置模拟**: 使用Android LocationManager的TestProvider功能
- **自动打卡**: 结合无障碍服务识别和点击打卡按钮
- **定时任务**: 使用ScheduledExecutorService实现定时检查
- **界面设计**: 采用Material Design设计规范

## 开发环境

- Android Studio 2022.3.1+
- Gradle 8.1.0
- Target SDK: 34
- Min SDK: 24

## 编译说明

1. 克隆项目到本地
2. 使用Android Studio打开项目
3. 等待Gradle同步完成
4. 连接Android设备或启动模拟器
5. 点击运行按钮编译安装

## 常见问题

**Q: 位置模拟不生效？**
A: 请确保在开发者选项中选择了正确的模拟位置应用

**Q: 无法自动点击打卡按钮？**
A: 请检查无障碍服务是否正确开启，不同版本的企业微信界面可能有差异

**Q: 应用闪退或无响应？**
A: 请检查权限设置，确保已授予所有必要权限

## 免责声明

本项目仅供技术学习和研究使用，开发者不对使用本软件造成的任何后果承担责任。使用者应当遵守相关法律法规和公司制度，合理合法地使用本软件。

## 许可证

本项目采用 MIT 许可证，详见 LICENSE 文件。
