@echo off
echo 企业微信打卡助手 - 编译脚本
echo ================================

echo 正在清理项目...
call gradlew clean

echo 正在编译项目...
call gradlew assembleDebug

if %ERRORLEVEL% EQU 0 (
    echo.
    echo 编译成功！
    echo APK文件位置: app\build\outputs\apk\debug\app-debug.apk
    echo.
    echo 安装说明:
    echo 1. 将APK文件传输到Android设备
    echo 2. 在设备上安装APK
    echo 3. 按照README.md中的说明配置权限
    echo.
) else (
    echo.
    echo 编译失败，请检查错误信息
    echo.
)

pause
