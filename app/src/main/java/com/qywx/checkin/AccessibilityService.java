package com.qywx.checkin;

import android.accessibilityservice.AccessibilityService;
import android.accessibilityservice.AccessibilityServiceInfo;
import android.content.Intent;
import android.util.Log;
import android.view.accessibility.AccessibilityEvent;
import android.view.accessibility.AccessibilityNodeInfo;

import java.util.List;

public class AccessibilityService extends android.accessibilityservice.AccessibilityService {
    
    private static final String TAG = "AccessibilityService";
    private static final String WEWORK_PACKAGE = "com.tencent.wework";
    
    @Override
    public void onAccessibilityEvent(AccessibilityEvent event) {
        if (event == null) return;
        
        String packageName = event.getPackageName() != null ? event.getPackageName().toString() : "";
        
        // 只处理企业微信的事件
        if (!WEWORK_PACKAGE.equals(packageName)) {
            return;
        }
        
        int eventType = event.getEventType();
        Log.d(TAG, "Accessibility event: " + eventType + " in " + packageName);
        
        // 当窗口内容改变时，尝试查找打卡按钮
        if (eventType == AccessibilityEvent.TYPE_WINDOW_CONTENT_CHANGED ||
            eventType == AccessibilityEvent.TYPE_WINDOW_STATE_CHANGED) {
            
            performCheckinIfPossible();
        }
    }
    
    private void performCheckinIfPossible() {
        AccessibilityNodeInfo rootNode = getRootInActiveWindow();
        if (rootNode == null) {
            Log.d(TAG, "Root node is null");
            return;
        }
        
        try {
            // 查找打卡相关的按钮或文本
            boolean found = findAndClickCheckinButton(rootNode);
            if (found) {
                Log.d(TAG, "Checkin button clicked");
            } else {
                Log.d(TAG, "Checkin button not found");
            }
        } catch (Exception e) {
            Log.e(TAG, "Error performing checkin", e);
        } finally {
            rootNode.recycle();
        }
    }
    
    private boolean findAndClickCheckinButton(AccessibilityNodeInfo node) {
        if (node == null) return false;
        
        // 检查当前节点是否是打卡按钮
        if (isCheckinButton(node)) {
            return performClick(node);
        }
        
        // 递归查找子节点
        int childCount = node.getChildCount();
        for (int i = 0; i < childCount; i++) {
            AccessibilityNodeInfo child = node.getChild(i);
            if (child != null) {
                boolean found = findAndClickCheckinButton(child);
                child.recycle();
                if (found) {
                    return true;
                }
            }
        }
        
        return false;
    }
    
    private boolean isCheckinButton(AccessibilityNodeInfo node) {
        if (node == null) return false;
        
        // 检查节点文本是否包含打卡相关关键词
        CharSequence text = node.getText();
        CharSequence contentDesc = node.getContentDescription();
        
        String[] checkinKeywords = {"打卡", "签到", "上班打卡", "下班打卡", "考勤打卡"};
        
        for (String keyword : checkinKeywords) {
            if ((text != null && text.toString().contains(keyword)) ||
                (contentDesc != null && contentDesc.toString().contains(keyword))) {
                
                // 确保节点是可点击的
                if (node.isClickable() || node.isEnabled()) {
                    Log.d(TAG, "Found checkin button with text: " + 
                          (text != null ? text : contentDesc));
                    return true;
                }
            }
        }
        
        return false;
    }
    
    private boolean performClick(AccessibilityNodeInfo node) {
        if (node == null) return false;
        
        try {
            if (node.isClickable()) {
                return node.performAction(AccessibilityNodeInfo.ACTION_CLICK);
            } else {
                // 如果节点不可点击，尝试找到可点击的父节点
                AccessibilityNodeInfo parent = node.getParent();
                while (parent != null) {
                    if (parent.isClickable()) {
                        boolean result = parent.performAction(AccessibilityNodeInfo.ACTION_CLICK);
                        parent.recycle();
                        return result;
                    }
                    AccessibilityNodeInfo temp = parent;
                    parent = parent.getParent();
                    temp.recycle();
                }
            }
        } catch (Exception e) {
            Log.e(TAG, "Error performing click", e);
        }
        
        return false;
    }
    
    @Override
    public void onInterrupt() {
        Log.d(TAG, "Accessibility service interrupted");
    }
    
    @Override
    protected void onServiceConnected() {
        super.onServiceConnected();
        Log.d(TAG, "Accessibility service connected");
        
        // 配置服务信息
        AccessibilityServiceInfo info = new AccessibilityServiceInfo();
        info.eventTypes = AccessibilityEvent.TYPE_WINDOW_CONTENT_CHANGED |
                         AccessibilityEvent.TYPE_WINDOW_STATE_CHANGED;
        info.feedbackType = AccessibilityServiceInfo.FEEDBACK_GENERIC;
        info.flags = AccessibilityServiceInfo.FLAG_REPORT_VIEW_IDS;
        info.packageNames = new String[]{WEWORK_PACKAGE};
        
        setServiceInfo(info);
    }
    
    // 提供静态方法供外部调用
    public static void triggerCheckin() {
        // 发送广播通知服务执行打卡
        Log.d(TAG, "Checkin triggered externally");
    }
}
