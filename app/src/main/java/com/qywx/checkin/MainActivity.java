package com.qywx.checkin;

import android.Manifest;
import android.app.AlertDialog;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.location.LocationManager;
import android.os.Bundle;
import android.provider.Settings;
import android.view.View;
import android.widget.Button;
import android.widget.EditText;
import android.widget.TextView;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;

public class MainActivity extends AppCompatActivity {
    
    private static final int PERMISSION_REQUEST_CODE = 1001;
    
    private EditText etLatitude, etLongitude, etAddress;
    private Button btnSetLocation, btnStartCheckin, btnStopCheckin;
    private TextView tvStatus;
    
    private LocationMockService locationService;
    private CheckinHelper checkinHelper;
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_main);
        
        initViews();
        initServices();
        checkPermissions();
        setupClickListeners();
    }
    
    private void initViews() {
        etLatitude = findViewById(R.id.et_latitude);
        etLongitude = findViewById(R.id.et_longitude);
        etAddress = findViewById(R.id.et_address);
        btnSetLocation = findViewById(R.id.btn_set_location);
        btnStartCheckin = findViewById(R.id.btn_start_checkin);
        btnStopCheckin = findViewById(R.id.btn_stop_checkin);
        tvStatus = findViewById(R.id.tv_status);
        
        // 设置默认位置（示例：北京某办公楼）
        etLatitude.setText("39.908823");
        etLongitude.setText("116.397470");
        etAddress.setText("北京市东城区天安门广场");
    }
    
    private void initServices() {
        locationService = new LocationMockService();
        checkinHelper = new CheckinHelper(this);
    }
    
    private void setupClickListeners() {
        btnSetLocation.setOnClickListener(v -> setMockLocation());
        btnStartCheckin.setOnClickListener(v -> startAutoCheckin());
        btnStopCheckin.setOnClickListener(v -> stopAutoCheckin());
    }
    
    private void checkPermissions() {
        String[] permissions = {
            Manifest.permission.ACCESS_FINE_LOCATION,
            Manifest.permission.ACCESS_COARSE_LOCATION,
            Manifest.permission.ACCESS_MOCK_LOCATION
        };
        
        boolean allGranted = true;
        for (String permission : permissions) {
            if (ContextCompat.checkSelfPermission(this, permission) != PackageManager.PERMISSION_GRANTED) {
                allGranted = false;
                break;
            }
        }
        
        if (!allGranted) {
            ActivityCompat.requestPermissions(this, permissions, PERMISSION_REQUEST_CODE);
        }
        
        checkMockLocationSettings();
    }
    
    private void checkMockLocationSettings() {
        LocationManager locationManager = (LocationManager) getSystemService(LOCATION_SERVICE);
        if (!locationManager.isProviderEnabled(LocationManager.GPS_PROVIDER)) {
            showLocationSettingsDialog();
        }
    }
    
    private void showLocationSettingsDialog() {
        new AlertDialog.Builder(this)
            .setTitle("位置服务")
            .setMessage("需要开启位置服务和模拟位置功能")
            .setPositiveButton("去设置", (dialog, which) -> {
                startActivity(new Intent(Settings.ACTION_LOCATION_SOURCE_SETTINGS));
            })
            .setNegativeButton("取消", null)
            .show();
    }
    
    private void setMockLocation() {
        try {
            double latitude = Double.parseDouble(etLatitude.getText().toString());
            double longitude = Double.parseDouble(etLongitude.getText().toString());
            String address = etAddress.getText().toString();
            
            boolean success = locationService.setMockLocation(this, latitude, longitude);
            if (success) {
                tvStatus.setText("位置已设置: " + address);
                Toast.makeText(this, "位置模拟成功", Toast.LENGTH_SHORT).show();
            } else {
                tvStatus.setText("位置设置失败");
                Toast.makeText(this, "位置模拟失败，请检查权限设置", Toast.LENGTH_SHORT).show();
            }
        } catch (NumberFormatException e) {
            Toast.makeText(this, "请输入正确的经纬度", Toast.LENGTH_SHORT).show();
        }
    }
    
    private void startAutoCheckin() {
        checkinHelper.startAutoCheckin();
        tvStatus.setText("自动打卡已启动");
        btnStartCheckin.setEnabled(false);
        btnStopCheckin.setEnabled(true);
        Toast.makeText(this, "自动打卡功能已启动", Toast.LENGTH_SHORT).show();
    }
    
    private void stopAutoCheckin() {
        checkinHelper.stopAutoCheckin();
        tvStatus.setText("自动打卡已停止");
        btnStartCheckin.setEnabled(true);
        btnStopCheckin.setEnabled(false);
        Toast.makeText(this, "自动打卡功能已停止", Toast.LENGTH_SHORT).show();
    }
    
    @Override
    public void onRequestPermissionsResult(int requestCode, String[] permissions, int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        if (requestCode == PERMISSION_REQUEST_CODE) {
            boolean allGranted = true;
            for (int result : grantResults) {
                if (result != PackageManager.PERMISSION_GRANTED) {
                    allGranted = false;
                    break;
                }
            }
            
            if (!allGranted) {
                Toast.makeText(this, "需要位置权限才能正常使用", Toast.LENGTH_LONG).show();
            }
        }
    }
}
