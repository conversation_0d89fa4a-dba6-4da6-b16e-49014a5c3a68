package com.qywx.checkin;

import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;
import android.widget.Toast;

import java.util.Calendar;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.TimeUnit;

public class CheckinHelper {
    
    private static final String TAG = "CheckinHelper";
    private static final String WEWORK_PACKAGE = "com.tencent.wework";
    
    private Context context;
    private ScheduledExecutorService scheduler;
    private ScheduledFuture<?> checkinTask;
    private Handler mainHandler;
    
    // 打卡时间配置
    private int morningHour = 9;    // 上班时间 9:00
    private int morningMinute = 0;
    private int eveningHour = 18;   // 下班时间 18:00
    private int eveningMinute = 0;
    
    public CheckinHelper(Context context) {
        this.context = context;
        this.scheduler = Executors.newScheduledThreadPool(1);
        this.mainHandler = new Handler(Looper.getMainLooper());
    }
    
    public void startAutoCheckin() {
        if (checkinTask != null && !checkinTask.isCancelled()) {
            checkinTask.cancel(false);
        }
        
        // 检查企业微信是否安装
        if (!isWeworkInstalled()) {
            showToast("未检测到企业微信应用");
            return;
        }
        
        // 每分钟检查一次是否需要打卡
        checkinTask = scheduler.scheduleAtFixedRate(this::checkAndPerformCheckin, 0, 1, TimeUnit.MINUTES);
        
        Log.d(TAG, "Auto checkin started");
    }
    
    public void stopAutoCheckin() {
        if (checkinTask != null) {
            checkinTask.cancel(false);
            checkinTask = null;
        }
        Log.d(TAG, "Auto checkin stopped");
    }
    
    private void checkAndPerformCheckin() {
        Calendar now = Calendar.getInstance();
        int currentHour = now.get(Calendar.HOUR_OF_DAY);
        int currentMinute = now.get(Calendar.MINUTE);
        
        // 检查是否到了打卡时间（允许5分钟误差）
        boolean isMorningTime = isTimeMatch(currentHour, currentMinute, morningHour, morningMinute);
        boolean isEveningTime = isTimeMatch(currentHour, currentMinute, eveningHour, eveningMinute);
        
        if (isMorningTime || isEveningTime) {
            String checkinType = isMorningTime ? "上班" : "下班";
            Log.d(TAG, "Time to checkin: " + checkinType);
            
            mainHandler.post(() -> {
                performCheckin(checkinType);
            });
        }
    }
    
    private boolean isTimeMatch(int currentHour, int currentMinute, int targetHour, int targetMinute) {
        // 计算时间差（分钟）
        int currentTotalMinutes = currentHour * 60 + currentMinute;
        int targetTotalMinutes = targetHour * 60 + targetMinute;
        int diff = Math.abs(currentTotalMinutes - targetTotalMinutes);
        
        // 允许5分钟误差
        return diff <= 5;
    }
    
    private void performCheckin(String type) {
        try {
            // 启动企业微信
            Intent intent = context.getPackageManager().getLaunchIntentForPackage(WEWORK_PACKAGE);
            if (intent != null) {
                intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                context.startActivity(intent);
                
                showToast("正在启动企业微信进行" + type + "打卡");
                
                // 延迟执行打卡操作，等待应用启动
                mainHandler.postDelayed(() -> {
                    performActualCheckin(type);
                }, 3000);
                
            } else {
                showToast("无法启动企业微信");
            }
            
        } catch (Exception e) {
            Log.e(TAG, "Failed to perform checkin", e);
            showToast("打卡失败: " + e.getMessage());
        }
    }
    
    private void performActualCheckin(String type) {
        // 这里需要使用无障碍服务来模拟点击打卡按钮
        // 由于每个企业的企业微信界面可能不同，这里提供基础框架
        
        showToast(type + "打卡提醒：请手动完成打卡操作");
        
        // TODO: 实现具体的自动点击逻辑
        // 1. 找到打卡按钮
        // 2. 模拟点击
        // 3. 确认打卡成功
        
        Log.d(TAG, "Checkin reminder sent for: " + type);
    }
    
    public void performManualCheckin() {
        performCheckin("手动");
    }
    
    private boolean isWeworkInstalled() {
        try {
            context.getPackageManager().getPackageInfo(WEWORK_PACKAGE, 0);
            return true;
        } catch (PackageManager.NameNotFoundException e) {
            return false;
        }
    }
    
    private void showToast(String message) {
        mainHandler.post(() -> {
            Toast.makeText(context, message, Toast.LENGTH_SHORT).show();
        });
    }
    
    // 设置打卡时间
    public void setMorningTime(int hour, int minute) {
        this.morningHour = hour;
        this.morningMinute = minute;
    }
    
    public void setEveningTime(int hour, int minute) {
        this.eveningHour = hour;
        this.eveningMinute = minute;
    }
    
    public void cleanup() {
        stopAutoCheckin();
        if (scheduler != null && !scheduler.isShutdown()) {
            scheduler.shutdown();
        }
    }
}
