package com.qywx.checkin;

import android.content.Context;
import android.location.Location;
import android.location.LocationManager;
import android.location.provider.ProviderProperties;
import android.os.Build;
import android.os.SystemClock;
import android.util.Log;

public class LocationMockService {
    
    private static final String TAG = "LocationMockService";
    private static final String MOCK_PROVIDER = LocationManager.GPS_PROVIDER;
    
    private LocationManager locationManager;
    private boolean isMocking = false;
    
    public boolean setMockLocation(Context context, double latitude, double longitude) {
        try {
            locationManager = (LocationManager) context.getSystemService(Context.LOCATION_SERVICE);
            
            // 添加模拟位置提供者
            if (!isMocking) {
                addMockLocationProvider();
            }
            
            // 创建模拟位置
            Location mockLocation = createMockLocation(latitude, longitude);
            
            // 设置模拟位置
            locationManager.setTestProviderLocation(MOCK_PROVIDER, mockLocation);
            
            isMocking = true;
            Log.d(TAG, "Mock location set: " + latitude + ", " + longitude);
            return true;
            
        } catch (SecurityException e) {
            Log.e(TAG, "Permission denied for mock location", e);
            return false;
        } catch (Exception e) {
            Log.e(TAG, "Failed to set mock location", e);
            return false;
        }
    }
    
    private void addMockLocationProvider() {
        try {
            // 移除现有的模拟提供者（如果存在）
            try {
                locationManager.removeTestProvider(MOCK_PROVIDER);
            } catch (Exception e) {
                // 忽略错误，可能提供者不存在
            }
            
            // 添加新的模拟提供者
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                // Android 12+ 使用新的API
                ProviderProperties properties = new ProviderProperties.Builder()
                    .setHasNetworkRequirement(false)
                    .setHasSatelliteRequirement(false)
                    .setHasCellRequirement(false)
                    .setHasMonetaryCost(false)
                    .setHasAltitudeSupport(true)
                    .setHasSpeedSupport(true)
                    .setHasBearingSupport(true)
                    .setPowerUsage(ProviderProperties.POWER_USAGE_LOW)
                    .setAccuracy(ProviderProperties.ACCURACY_FINE)
                    .build();
                
                locationManager.addTestProvider(MOCK_PROVIDER, properties);
            } else {
                // Android 11及以下使用旧API
                locationManager.addTestProvider(
                    MOCK_PROVIDER,
                    false,  // requiresNetwork
                    false,  // requiresSatellite
                    false,  // requiresCell
                    false,  // hasMonetaryCost
                    true,   // supportsAltitude
                    true,   // supportsSpeed
                    true,   // supportsBearing
                    android.location.Criteria.POWER_LOW,
                    android.location.Criteria.ACCURACY_FINE
                );
            }
            
            // 启用模拟提供者
            locationManager.setTestProviderEnabled(MOCK_PROVIDER, true);
            
        } catch (Exception e) {
            Log.e(TAG, "Failed to add mock location provider", e);
            throw e;
        }
    }
    
    private Location createMockLocation(double latitude, double longitude) {
        Location location = new Location(MOCK_PROVIDER);
        location.setLatitude(latitude);
        location.setLongitude(longitude);
        location.setAltitude(0);
        location.setAccuracy(1.0f);
        location.setSpeed(0);
        location.setBearing(0);
        
        // 设置时间戳
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN_MR1) {
            location.setElapsedRealtimeNanos(SystemClock.elapsedRealtimeNanos());
        }
        location.setTime(System.currentTimeMillis());
        
        return location;
    }
    
    public void stopMockLocation(Context context) {
        try {
            if (locationManager == null) {
                locationManager = (LocationManager) context.getSystemService(Context.LOCATION_SERVICE);
            }
            
            locationManager.removeTestProvider(MOCK_PROVIDER);
            isMocking = false;
            Log.d(TAG, "Mock location stopped");
            
        } catch (Exception e) {
            Log.e(TAG, "Failed to stop mock location", e);
        }
    }
    
    public boolean isMocking() {
        return isMocking;
    }
}
