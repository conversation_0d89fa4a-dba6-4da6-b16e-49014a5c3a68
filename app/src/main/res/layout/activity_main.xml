<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:padding="16dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <!-- 标题 -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="企业微信打卡助手"
            android:textSize="24sp"
            android:textStyle="bold"
            android:gravity="center"
            android:layout_marginBottom="24dp"
            android:textColor="@android:color/black" />

        <!-- 位置设置区域 -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardCornerRadius="8dp"
            app:cardElevation="4dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="位置设置"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:layout_marginBottom="12dp" />

                <com.google.android.material.textfield.TextInputLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="8dp"
                    android:hint="纬度">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/et_latitude"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:inputType="numberDecimal|numberSigned" />

                </com.google.android.material.textfield.TextInputLayout>

                <com.google.android.material.textfield.TextInputLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="8dp"
                    android:hint="经度">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/et_longitude"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:inputType="numberDecimal|numberSigned" />

                </com.google.android.material.textfield.TextInputLayout>

                <com.google.android.material.textfield.TextInputLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="12dp"
                    android:hint="地址描述">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/et_address"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:inputType="text" />

                </com.google.android.material.textfield.TextInputLayout>

                <Button
                    android:id="@+id/btn_set_location"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="设置位置"
                    android:backgroundTint="@android:color/holo_blue_dark" />

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

        <!-- 打卡控制区域 -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardCornerRadius="8dp"
            app:cardElevation="4dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="打卡控制"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:layout_marginBottom="12dp" />

                <Button
                    android:id="@+id/btn_start_checkin"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="启动自动打卡"
                    android:layout_marginBottom="8dp"
                    android:backgroundTint="@android:color/holo_green_dark" />

                <Button
                    android:id="@+id/btn_stop_checkin"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="停止自动打卡"
                    android:enabled="false"
                    android:backgroundTint="@android:color/holo_red_dark" />

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

        <!-- 状态显示区域 -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:cardCornerRadius="8dp"
            app:cardElevation="4dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="状态信息"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:layout_marginBottom="12dp" />

                <TextView
                    android:id="@+id/tv_status"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="等待操作..."
                    android:textSize="14sp"
                    android:padding="8dp"
                    android:background="@android:color/darker_gray"
                    android:textColor="@android:color/white" />

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

        <!-- 使用说明 -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="24dp"
            android:text="使用说明：\n1. 在开发者选项中启用模拟位置应用\n2. 输入公司的经纬度坐标\n3. 点击设置位置进行位置模拟\n4. 启动自动打卡功能\n5. 应用会在上班(9:00)和下班(18:00)时间自动提醒打卡"
            android:textSize="12sp"
            android:lineSpacingExtra="4dp"
            android:textColor="@android:color/darker_gray" />

    </LinearLayout>

</ScrollView>
